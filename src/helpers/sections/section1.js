// Section 1 form -> payload helpers
// Standard naming and export convention: named exports from helpers module

// Convert various date inputs to 'YYYY-MM-DD HH:mm:ss'
export const toDateTime = (dateStr) => {
  if (!dateStr) return '';
  if (/(\d{4}-\d{2}-\d{2})\s+\d{2}:\d{2}:\d{2}/.test(dateStr)) return dateStr;
  if (/^\d{4}-\d{2}-\d{2}$/.test(dateStr)) return `${dateStr} 00:00:00`;
  try {
    const d = new Date(dateStr);
    const pad = (n) => `${n}`.padStart(2, '0');
    const yyyy = d.getFullYear();
    const mm = pad(d.getMonth() + 1);
    const dd = pad(d.getDate());
    const hh = pad(d.getHours());
    const mi = pad(d.getMinutes());
    const ss = pad(d.getSeconds());
    return `${yyyy}-${mm}-${dd} ${hh}:${mi}:${ss}`;
  } catch {
    return '';
  }
};


export const mapSection1ToPayload = (form, ctx = {}) => {
  const {
    currentHospital = '',
    TNRPatient = ''
  } = ctx;

  // Base payload structure
  const payload = {
    num: '1',
    data: {
      TNR: TNRPatient,
      hospital: currentHospital || 'vernity',
      admission_status: 'admitted',
      intrauterine_transter: '',
      transfer_member: '',
      hospital_name: '',
      non_hospital_name: '',
      admitted_to: form?.admitted_to || '',
      admission_date: toDateTime(form?.admission_date)?.split(' ')[0] || '', // Date only
      discharge_date: toDateTime(form?.discharge_date)?.split(' ')[0] || ''||'',
      stay_day: form?.hospital_stay ? String(form.hospital_stay) : '0',
      discharge_type: '',
      discharge_hospital: '',
      discharge_hospital_code: '',
      discharge_level2_hospital: '',
    }
  };

  // Handle admission type (inborn/outborn)
  if (form?.admission_type === 'inborn') {
    payload.data.admission_status = 'inborn';
    payload.data.intrauterine_transter = form?.inborn_transfer_type || '';
  } else if (form?.admission_type === 'outborn') {
    payload.data.admission_status = 'outborn';
    if (form?.outborn_transfer_type === 'tnr') {
      payload.data.transfer_member = 'TNR member hospital';
      payload.data.hospital_name = form?.tnr_hospital_name ||""
    } else if (form?.outborn_transfer_type === 'non_tnr') {
      payload.data.transfer_member = 'Non TNR member hospital';
      payload.data.non_hospital_name = form?.non_tnr_hospital_name || '';
    }
  } else if (form?.outborn_transfer_type === 'bba') {
    payload.data.admission_status = 'BBA';
  }

  // Handle discharge type
  if (form?.discharge_status === 'discharge_home') {
    payload.data.discharge_type = 'home';
  } 
  else if (form?.discharge_status === 'death') {
    payload.data.discharge_type = 'death';
  } 
  else if (form?.discharge_status === 'refer_tnr') {
    payload.data.discharge_type = 'refer_tnr';
    payload.data.discharge_hospital = form?.discharge_tnr_hospital || '';
    payload.data.discharge_hospital_code = form?.discharge_tnr_hospital_code || '';
  }
  else if (form?.discharge_status === 'refer_level2') {
    payload.data.discharge_type = 'transfer';
    payload.data.discharge_level2_hospital = form?.discharge_level2_hospital || '';
  }

  return payload;
};
