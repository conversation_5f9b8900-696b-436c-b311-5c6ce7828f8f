export const mapSection3Data = (formData, TNR) => {
    const boolToYesNo = (value) => value ? "yes" : "no";
    const naToYesNo = (value) => value ? "yes" : "no";

    // --- Delivery Mode ---
    let deliveryMode = "";
    let vaginal = "no";
    let vaginal_with_instrument = "no";
    let vaginal_without_instrument = "no";

    if (formData.mode_of_delivery === "vaginal") {
        deliveryMode = "vaginal";
        vaginal = "yes";

        // Map vaginal type
        if (formData.vaginal_type === "with_instrument") {
            vaginal = "with instrument";
        } else if (formData.vaginal_type === "without_instrument") {
            vaginal = "without instrument";
        }
    } else if (formData.mode_of_delivery === "caesarean_section") {
        deliveryMode = "caesarean";
    }

    // --- Caesarean Types ---
    const caesareanTypes = formData.caesarean_types || [];
    const elective = caesareanTypes.includes("elective_previous") ? "yes" : "no";
    const emergency = caesareanTypes.includes("emergency") ? "yes" : "no";
    const cephalopelvic = caesareanTypes.includes("cephalopelvic_disproportion") ? "yes" : "no";
    const caesarean_other = caesareanTypes.includes("other") ? "yes" : "no";
    const caesarean_other_text =
        caesarean_other === "yes" ? formData.caesarean_other_specify || "" : "";

    // --- Apgar ---
    const apgar_1min = formData.apgar_1min_na ? "NA" : (formData.apgar_1min || "");
    const apgar_5min = formData.apgar_5min_na ? "NA" : (formData.apgar_5min || "");
    const apgar_10min = formData.apgar_10min_na ? "NA" : (formData.apgar_10min || "");

    // NEW: add naToYesNo for 1 min
    const apgar_1min_NA = naToYesNo(formData.apgar_1min_na);
    const apgar_5min_NA = naToYesNo(formData.apgar_5min_na);
    const apgar_10min_NA = naToYesNo(formData.apgar_10min_na);

    // --- Resuscitation ---
    const resuscitation =
        formData.resuscitation === "yes"
            ? "yes"
            : formData.resuscitation === "no"
            ? "no"
            : "NA";

    const CPAP = resuscitation === "yes" ? boolToYesNo(formData.resuscitation_cpap) : "no";
    const PPV = resuscitation === "yes" ? boolToYesNo(formData.resuscitation_ppv) : "no";
    const intubation = resuscitation === "yes" ? boolToYesNo(formData.resuscitation_intubation) : "no";
    const chest_compression = resuscitation === "yes" ? boolToYesNo(formData.resuscitation_chest_compression) : "no";
    const epinephrine = resuscitation === "yes" ? boolToYesNo(formData.resuscitation_epinephrine) : "no";

    // --- Cord blood ---
    const cord_blood =
        formData.cord_blood_gas === "available"
            ? "yes"
            : formData.cord_blood_gas === "not_done"
            ? "no"
            : "NA";

    // --- Delayed cord clamping ---
    const delayed_cord_clamping =
        formData.delayed_cord_clamping === "yes"
            ? "yes"
            : formData.delayed_cord_clamping === "no"
            ? "no"
            : "NA";

    // --- Build payload only with non-empty fields ---
    const data = {};
    const addIfValid = (key, value) => {
        if (
            value !== undefined &&
            value !== null &&
            value !== "" &&
            value !== "no"
        ) {
            data[key] = value;
        }
    };

    // Delivery mode
    addIfValid("delivery_mode", deliveryMode);
    addIfValid("vaginal", vaginal);
    addIfValid("vaginal_with_instrument", vaginal_with_instrument);
    addIfValid("vaginal_without_instrument", vaginal_without_instrument);

    // Caesarean
    addIfValid("elective", elective);
    addIfValid("emergency", emergency);
    addIfValid("cephalopelvic", cephalopelvic);
    addIfValid("caesarean_other", caesarean_other);
    addIfValid("caesarean_other_text", caesarean_other_text);

    // Apgar
    addIfValid("1_min_score", apgar_1min);
    addIfValid("1_min_NA", apgar_1min_NA);   // ✅ new line added
    addIfValid("5_min_score", apgar_5min);
    addIfValid("5_min_NA", apgar_5min_NA);
    addIfValid("10_min_score", apgar_10min);
    addIfValid("10_min_NA", apgar_10min_NA);

    // Resuscitation
    addIfValid("resuscitation", resuscitation);
    addIfValid("CPAP", CPAP);
    addIfValid("PPV", PPV);
    addIfValid("intubation", intubation);
    addIfValid("chest_compression", chest_compression);
    addIfValid("epinephrine", epinephrine);

    // Cord blood
    addIfValid("cord_blood", cord_blood);
    if (cord_blood === "yes") {
        addIfValid("cord_blood_pH", formData.cord_blood_ph);
        addIfValid("cord_blood_pCO2", formData.cord_blood_pco2);
        addIfValid("cord_blood_HCO2", formData.cord_blood_hco2);
        addIfValid("cord_blood_BE", formData.cord_blood_be);
    }

    // Delayed cord clamping
    addIfValid("delayed_cord_clamping", delayed_cord_clamping);

    return {
        TNR: TNR,
        data,
    };
};



export default mapSection3Data;