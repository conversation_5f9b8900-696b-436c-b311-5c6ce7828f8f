export const mapSection4Data = (formData, name, TNR, hospital) => {
    const now = new Date().toISOString();
    const username = name || 'system';

    // Map growth status to the expected format
    const mapGrowthStatus = (status) => {
        switch (status) {
            case 'sga':
                return 'SGA (< 10th percentile)';
            case 'aga':
                return 'AGA (10-90th percentile)';
            case 'lga':
                return 'LGA (> 90th percentile)';
            default:
                return '';
        }
    };

    // Format birth date from day/month/year
    const formatBirthDate = (day, month, year) => {
        if (!day || !month || !year) return '';
        const paddedDay = day.padStart(2, '0');
        const paddedMonth = month.padStart(2, '0');
        return `${year}-${paddedMonth}-${paddedDay}`;
    };

    // Format birth time from hour/minute
    const formatBirthTime = (hour, minute) => {
        if (!hour || !minute) return '';
        const paddedHour = hour.padStart(2, '0');
        const paddedMinute = minute.padStart(2, '0');
        return `${paddedHour}:${paddedMinute}`;
    };

    // Format gestational age
    const formatGestationalAge = (weeks, days) => {
        if (!weeks && !days) return '';
        const weeksStr = weeks || '0';
        const daysStr = days || '0';
        return `${weeksStr} weeks ${daysStr} days`;
    };

    const payload = {
        num: '4',
        data: {
            TNR: TNR || '',
            hospital: hospital || 'vernity',
            birth_date: formatBirthDate(formData.birth_day, formData.birth_month, formData.birth_year),
            birth_time: formatBirthTime(formData.birth_hour, formData.birth_minute),
            gender: formData.gender || '',
            gestational_age: formatGestationalAge(formData.gestational_weeks, formData.gestational_days),
            birth_weight: formData.birth_weight || '',
            length: formData.length_na ? 'N/A' : (formData.length || ''),
            head_circumference: formData.head_circumference_na ? 'N/A' : (formData.head_circumference || ''),
            growth_status: mapGrowthStatus(formData.growth_status),
            created_at: now,
            updated_at: now,
            created_by: username,
            updated_by: username,
        }
    };

    return payload;
};

export default mapSection4Data;
