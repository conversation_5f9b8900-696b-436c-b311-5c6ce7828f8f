import { z } from 'zod';

export const section3Schema = z.object({
  mode_of_delivery: z.enum(['vaginal', 'caesarean_section']).nullable(),
  vaginal_type: z.enum(['with_instrument', 'without_instrument']).nullable().optional(),
  caesarean_types: z.array(z.string()).default([]),
  apgar_1min: z.string().optional(),
  apgar_5min: z.string().optional(),
  apgar_10min: z.string().optional(),
  apgar_1min_na: z.boolean().default(false),
  apgar_5min_na: z.boolean().default(false),
  apgar_10min_na: z.boolean().default(false),
  resuscitation: z.enum(['no', 'yes', 'na']).nullable(),
  resuscitation_cpap: z.boolean().default(false),
  resuscitation_ppv: z.boolean().default(false),
  resuscitation_intubation: z.boolean().default(false),
  resuscitation_chest_compression: z.boolean().default(false),
  resuscitation_epinephrine: z.boolean().default(false),
  cord_blood_gas: z.enum(['not_done', 'available', 'na']).nullable(),
  cord_blood_ph: z.string().optional(),
  cord_blood_pco2: z.string().optional(),
  cord_blood_hco2: z.string().optional(),
  cord_blood_be: z.string().optional(),
  delayed_cord_clamping: z.enum(['no', 'yes', 'na']).nullable(),
});

export const section3DefaultValues = {
  mode_of_delivery: null,
  vaginal_type: null,
  caesarean_types: [],
  apgar_1min: '',
  apgar_5min: '',
  apgar_10min: '',
  apgar_1min_na: false,
  apgar_5min_na: false,
  apgar_10min_na: false,
  resuscitation: null,
  resuscitation_cpap: false,
  resuscitation_ppv: false,
  resuscitation_intubation: false,
  resuscitation_chest_compression: false,
  resuscitation_epinephrine: false,
  cord_blood_gas: null,
  cord_blood_ph: '',
  cord_blood_pco2: '',
  cord_blood_hco2: '',
  cord_blood_be: '',
  delayed_cord_clamping: null,
};