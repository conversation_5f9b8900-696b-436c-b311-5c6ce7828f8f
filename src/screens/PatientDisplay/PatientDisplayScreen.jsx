import React, { useContext, useEffect } from 'react';
import { View, Text, ScrollView, TouchableOpacity, StyleSheet } from 'react-native';
import Ionicons from 'react-native-vector-icons/Ionicons';
import * as Progress from 'react-native-progress';
import { SafeAreaView } from 'react-native-safe-area-context';
import colors from '../../theme/colors';
import { useDispatch, useSelector } from 'react-redux';
import { fetchPatientByTnrHospital } from '../../features/patients/patientsSlice';
import { fetchRegisterProgressThunk, resetCriteria } from '../../features/register/registerSlice';
import { fetchSectionProgressThunk } from '../../features/sections/sectionSlice';
import { useRouter } from 'expo-router';
import ROUTES from '../../config/routes';
import LanguageContext from '../../context/LanguageContext';

export default function PatientDisplayScreen({ navigation }) {
    const router = useRouter();
    const dispatch = useDispatch();
    const { TNR, registerProgress } = useSelector((state) => state.register);
    const { selectedPatient } = useSelector((state) => state.patients);
    const { sectionProgress } = useSelector((state) => state.sections);
    const { user } = useSelector((state) => state.auth);
    const { t } = useContext(LanguageContext)

    useEffect(() => {
        const hospital = user?.hospital
        dispatch(fetchPatientByTnrHospital({ TNR: TNR, hospital: hospital }));
        dispatch(fetchRegisterProgressThunk(TNR));
        dispatch(fetchSectionProgressThunk({ TNR: TNR, hospital: hospital }));
    }, []);
    const patient = {
        name: selectedPatient?.fullname || "",
        tn: selectedPatient?.TNR || "",
        hn: selectedPatient?.HN || "",
        mother: selectedPatient?.mother_fullname || "",
    };


    const registrationData = {
        status: registerProgress?.[0]?.status === 'done' ? 'completed' : 'not_started'
    };

    // Inform Consent status (defaults to not started if not available)
    const consentData = {
        // status: registerProgress?.[0]?.consent_status === 'done' ? 'completed' : 'not_started'
    };

    const sectionsData = [
        {
            id: 'section1',
            label: 'Section 1: Admission data',
            status: sectionProgress?.[0]?.section1 === 'done' ? 'completed' : 'not_started'
        },
        {
            id: 'section2',
            label: 'Section 2: Maternal Data',
            status: sectionProgress?.[0]?.section2 === 'done' ? 'completed' : 'not_started'
        },
        {
            id: 'section3',
            label: 'Section 3: Perinatal Data',
            status: sectionProgress?.[0]?.section3 === 'done' ? 'completed' : 'not_started'
        },
        {
            id: 'section4',
            label: 'Section 4: Neonatal Data',
            status: sectionProgress?.[0]?.section4 === 'done' ? 'completed' : 'not_started'
        },
        {
            id: 'section5',
            label: 'Section 5: Hospital course',
            status: 'pending',
            subSections: [
                { id: 'section5_1', status: sectionProgress?.[0]?.section5_1 === 'done' ? 'completed' : 'not_started' },
                { id: 'section5_2', status: sectionProgress?.[0]?.section5_2 === 'done' ? 'completed' : 'not_started' },
                { id: 'section5_3', status: sectionProgress?.[0]?.section5_3 === 'done' ? 'completed' : 'not_started' },
                { id: 'section5_4', status: sectionProgress?.[0]?.section5_4 === 'done' ? 'completed' : 'not_started' },
                { id: 'section5_5', status: sectionProgress?.[0]?.section5_5 === 'done' ? 'completed' : 'not_started' },
                { id: 'section5_6', status: sectionProgress?.[0]?.section5_6 === 'done' ? 'completed' : 'not_started' },
                { id: 'section5_7', status: sectionProgress?.[0]?.section5_7 === 'done' ? 'completed' : 'not_started' },
                { id: 'section5_8', status: sectionProgress?.[0]?.section5_8 === 'done' ? 'completed' : 'not_started' },
                { id: 'section5_9', status: sectionProgress?.[0]?.section5_9 === 'done' ? 'completed' : 'not_started' },
                { id: 'section5_10', status: sectionProgress?.[0]?.section5_10 === 'done' ? 'completed' : 'not_started' },
                { id: 'section5_11', status: sectionProgress?.[0]?.section5_11 === 'done' ? 'completed' : 'not_started' },
                { id: 'section5_12', status: sectionProgress?.[0]?.section5_12 === 'done' ? 'completed' : 'not_started' },
                { id: 'section5_normal_newborn', status: sectionProgress?.[0]?.section5_normal_newborn === 'done' ? 'completed' : 'not_started' },
                { id: 'section5_sicknewborn', status: sectionProgress?.[0]?.section5_sicknewborn === 'done' ? 'completed' : 'not_started' }
            ]
        },
        {
            id: 'section6',
            label: 'Section 6: Short-term Outcomes',
            status: sectionProgress?.[0]?.section6 === 'done' ? 'completed' : 'not_started'
        },
    ];

    // Calculate progress percentage
    const calculateProgress = () => {
        let totalItems = 1; // Registration counts as 1
        let completedItems = registerProgress?.[0]?.status === 'done' ? 1 : 0;

        sectionsData.forEach(section => {
            if (section.subSections) {
                // Section 5 with subsections
                totalItems += section.subSections.length;
                completedItems += section.subSections.filter(sub => sub.status === 'completed').length;
            } else {
                // Regular sections
                totalItems += 1;
                if (section.status === 'completed') {
                    completedItems += 1;
                }
            }
        });

        return totalItems > 0 ? completedItems / totalItems : 0;
    };

    const progress = calculateProgress();

    // Get icon based on status
    const getStatusIcon = (status, hasSubSections = false, subSections = null) => {
        if (hasSubSections && subSections) {
            const completedSubs = subSections.filter(sub => sub.status === 'completed').length;
            const totalSubs = subSections.length;

            if (completedSubs === totalSubs) {
                return <Ionicons name="checkmark-circle" size={20} color="#7FC5C6" />;
            }
            // else if (completedSubs > 0) {
            //     return <Ionicons name="ellipse" size={18} color="#FFA500" />;
            // } 
            else {
                return <Ionicons name="ellipse-outline" size={20} color="#45C2BE" />;
            }
        }

        switch (status) {
            case 'completed':
                return <Ionicons name="checkmark-circle" size={20} color="#7FC5C6" />;
            // case 'pending':
            //     return <Ionicons name="ellipse" size={18} color="#FFA500" />;
            default:
                return <Ionicons name="ellipse-outline" size={18} color="#45C2BE" />;
        }
    };

    const handleBack = () => {
        router.replace(ROUTES.PRIVATE.PATIENT_LIST)
        dispatch(resetCriteria())
    };

    const handleAction = () => {
        // console.log('Top-right action clicked');
    };

    const handleSubmit = () => {
        // console.log('Submit Data Pressed');
    };

    const handleReferPress = () => {
        router.push({
            pathname: ROUTES.PRIVATE.REFER_LIST,
            params: { TNR, hospital: user?.hospital }
        });
    };

    const handleSectionPress = (section) => {
        if (section.id === 'section1') {
            router.push({
                pathname: ROUTES.PRIVATE.SECTION1_FORM,
                params: { TNR, hospital: user?.hospital }
            });
        }
        else if(section.id === 'section2'){
            router.push({
                pathname: ROUTES.PRIVATE.SECTION2_FORM,
                params: { TNR, hospital: user?.hospital }
            });
        }
        else if(section.id === 'section3'){
            router.push({
                pathname: ROUTES.PRIVATE.SECTION3_FORM,
                params: { TNR, hospital: user?.hospital }
            });
        }
        else if(section.id === 'section4'){
            router.push({
                pathname: ROUTES.PRIVATE.SECTION4_FORM,
                params: { TNR, hospital: user?.hospital }
            });
        }
    };

    const handleRegistrationPress = () => {
        router.push({
            pathname: '/privateRoutes/registration',
            params: { TNR, hospital: user?.hospital }
        });
    };

    const handleConsentPress = () => {
        // Navigate to Inform Consent screen (ensure route exists in your router)
        router.push({
            pathname: ROUTES.PRIVATE.INFORM_CONSENT,
            params: { TNR, hospital: user?.hospital }
        });
    };

    return (
        <View style={styles.container}>
            <View style={styles.topSafeArea} />

            {/* Header */}
            <View style={styles.header}>
                <View style={styles.headerTop}>
                    <TouchableOpacity onPress={handleBack} style={styles.backButton}>
                        <Ionicons name="chevron-back" size={24} color="#7FC5C6" />
                        <Text style={styles.backText}>{t.t("Back")}</Text>
                    </TouchableOpacity>

                    <TouchableOpacity onPress={handleReferPress} style={styles.referButton}>
                        <Text style={styles.referText}>{t.t("Refer")}</Text>
                    </TouchableOpacity>
                </View>

                <View style={styles.headerContent}>
                    <Text style={styles.patientName}>{patient.name}</Text>
                    <Text style={styles.patientInfo}>TN#: {patient.tn}</Text>
                    <Text style={styles.patientInfo}>HN: {patient.hn}</Text>
                    <Text style={styles.patientInfo}>{t.t("Mother")}: {patient.mother}</Text>
                </View>
            </View>

            {/* Body */}
            <ScrollView contentContainerStyle={styles.scrollContent}>
                {/* Progress */}
                <View style={styles.progressSection}>
                    <View style={styles.progressHeader}>
                        <Text style={styles.progressHeading}>{t.t("Progress")}</Text>
                        <Text style={styles.progressHeading}>{Math.round(progress * 100)}%</Text>
                    </View>
                    <Progress.Bar
                        progress={progress}
                        width={null}
                        color={colors.progress.primary}
                        unfilledColor="#eee"
                        borderWidth={0}
                        height={10}
                    />
                </View>


                {/* Registration Section */}
                <Text style={styles.sectionHeading}>{t.t("Registration Form")}</Text>
                <TouchableOpacity style={styles.row} onPress={handleRegistrationPress}>
                    {getStatusIcon(registrationData.status)}
                    <Text style={styles.rowLabel}>Registration</Text>
                    <Ionicons name="chevron-forward" size={20} color="#aaa" />
                </TouchableOpacity>

                {/* Inform Consent Row */}
                <TouchableOpacity style={styles.row} onPress={handleConsentPress}>
                    {getStatusIcon(consentData.status)}
                    <Text style={styles.rowLabel}>Inform Consent</Text>
                    <Ionicons name="chevron-forward" size={20} color="#aaa" />
                </TouchableOpacity>

                {/* Medical Section List */}
                <Text style={styles.sectionHeading}>{t.t("Medical data form")}</Text>
                {sectionsData.map((section, index) => (
                    <TouchableOpacity key={index} style={styles.row} onPress={() => handleSectionPress(section)}>
                        {getStatusIcon(section.status, !!section.subSections, section.subSections)}
                        <Text style={styles.rowLabel}>{section.label}</Text>
                        <Ionicons name="chevron-forward" size={20} color="#aaa" />
                    </TouchableOpacity>
                ))}
            </ScrollView>

            {/* Footer
            <View style={styles.footer}>
                <TouchableOpacity style={styles.submitButton} onPress={handleSubmit}>
                    <Text style={styles.submitButtonText}>Submit Data</Text>
                </TouchableOpacity>
            </View>
            <SafeAreaView style={styles.bottomSafeArea} edges={["bottom"]} /> */}
        </View>
    );
}
const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor:"#fff"
    },
    header: {
        // paddingHorizontal: 20,
        paddingBottom: 12,
        backgroundColor: '#F8F8F8',
        borderBottomWidth: 1,
        borderBottomColor: '#eee',
    },
    headerTop: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        padding: 5
    },
    headerContent: {
        paddingHorizontal: 20
    },
    backButton: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    backText: {
        color: '#7FC5C6',
        fontSize: 16,
        marginLeft: 2,
    },
    patientName: {
        fontSize: 17,
        fontWeight: 'bold',
        color: '#000',
        marginBottom: 6,
    },
    patientInfo: {
        fontSize: 14,
        color: '#444',
        marginBottom: 2,
    },

    scrollContent: {
        paddingLeft: 20,
        paddingBottom: 30,
    },

    sectionHeading: {
        fontSize: 15,
        fontWeight: 'bold',
        marginVertical: 16,
        color: '#000',
    },
    progressHeading: {
        fontSize: 15,
        fontWeight: 'bold',
        marginVertical: 10,
        color: '#000',
    },

    progressSection: {
        marginTop: 10,
        paddingRight: 20,
    },

    progressHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
    },


    row: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingVertical: 12,
        borderBottomWidth: 1,
        borderBottomColor: '#eee',
        paddingRight: 20
        // paddingHorizontal:20
    },
    rowLabel: {
        flex: 1,
        marginLeft: 10,
        fontSize: 14,
        color: '#222',
    },

    divider: {
        height: 1,
        backgroundColor: '#eee',
        marginVertical: 10,
    },

    footer: {
        padding: 20,
        borderTopWidth: 1,
        borderTopColor: '#eee',
        backgroundColor: '#fff',
    },
    submitButton: {
        backgroundColor: colors.button.primary,
        paddingVertical: 14,
        borderRadius: 24,
        alignItems: 'center',
    },
    submitButtonText: {
        color: '#fff',
        fontWeight: 'bold',
        fontSize: 16,
    },
    bottomSafeArea: {
        backgroundColor: '#fff',
    },
    topSafeArea: {
        height: 48,
        backgroundColor: '#F8F8F8',
    },
    referButton: {
        backgroundColor: '#7FC5C6',
        paddingHorizontal: 14,
        paddingVertical: 6,
        borderRadius: 16,
        marginRight: 8,
    },
    referText: {
        color: '#fff',
        fontWeight: 'bold',
    },
});
