export const getMonthOptions = () => {
  return Array.from({ length: 12 }, (_, i) => {
    const month = i + 1;
    return { label: `${month}`, value: `${month}`.padStart(2, '0') };
  });
};

export const getYearOptionsRange = (pastYears = 4, futureYears = 10) => {
  const currentYear = new Date().getFullYear();
  const start = currentYear - pastYears;
  const end = currentYear + futureYears;
  const total = end - start + 1;
  return Array.from({ length: total }, (_, i) => {
    const year = start + i;
    return { label: `${year}`, value: `${year}` };
  });
};

export const getDayOptions = (year, month) => {
  const m = month ? parseInt(month, 10) : null;
  const y = year ? parseInt(year, 10) : null;

  let max;
  if (!m) {
    max = 31; // default if month not provided
  } else if (!y) {
    // No year → assume non-leap for Feb
    max = m === 2 ? 28 : [4, 6, 9, 11].includes(m) ? 30 : 31;
  } else {
    // JS trick: day 0 of next month = last day of target month
    max = new Date(y, m, 0).getDate();
  }

  return Array.from({ length: max }, (_, i) => {
    const day = i + 1;
    return { label: `${day}`, value: `${day}`.padStart(2, '0') };
  });
};

